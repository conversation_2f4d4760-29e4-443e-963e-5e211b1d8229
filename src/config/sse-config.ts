/**
 * SSE 配置文件
 * 集中管理 SSE 相关的配置参数
 */

/**
 * SSE 处理器配置
 */
export interface SSEProcessorConfig {
  /** 是否启用调试日志 */
  enableDebugLogs: boolean;
  /** 最大缓冲区大小（字节） */
  maxBufferSize: number;
  /** JSON 解析重试次数 */
  jsonParseRetries: number;
  /** 消息去重缓存大小 */
  deduplicationCacheSize: number;
  /** 是否启用消息去重 */
  enableDeduplication: boolean;
}

/**
 * SSE 重连配置
 */
export interface SSEReconnectConfig {
  /** 最大重连次数 */
  maxRetries: number;
  /** 初始重连延迟（毫秒） */
  initialDelay: number;
  /** 最大重连延迟（毫秒） */
  maxDelay: number;
  /** 延迟倍增因子 */
  backoffFactor: number;
  /** 连接超时时间（毫秒） */
  connectionTimeout: number;
  /** 是否启用自动重连 */
  enableAutoReconnect: boolean;
}

/**
 * SSE 监控配置
 */
export interface SSEMonitoringConfig {
  /** 是否启用性能监控 */
  enablePerformanceMonitoring: boolean;
  /** 是否启用错误统计 */
  enableErrorTracking: boolean;
  /** 统计报告间隔（毫秒） */
  reportingInterval: number;
  /** 是否记录详细的事件日志 */
  enableEventLogging: boolean;
}

/**
 * 完整的 SSE 配置
 */
export interface SSEConfig {
  processor: SSEProcessorConfig;
  reconnect: SSEReconnectConfig;
  monitoring: SSEMonitoringConfig;
}

/**
 * 默认 SSE 配置
 */
export const DEFAULT_SSE_CONFIG: SSEConfig = {
  processor: {
    enableDebugLogs: process.env.NODE_ENV === 'development',
    maxBufferSize: 1024 * 1024, // 1MB
    jsonParseRetries: 3,
    deduplicationCacheSize: 1000,
    enableDeduplication: true
  },
  reconnect: {
    maxRetries: 5,
    initialDelay: 1000,
    maxDelay: 30000,
    backoffFactor: 2,
    connectionTimeout: 10000,
    enableAutoReconnect: true
  },
  monitoring: {
    enablePerformanceMonitoring: process.env.NODE_ENV === 'development',
    enableErrorTracking: true,
    reportingInterval: 60000, // 1分钟
    enableEventLogging: process.env.NODE_ENV === 'development'
  }
};

/**
 * 生产环境优化配置
 */
export const PRODUCTION_SSE_CONFIG: SSEConfig = {
  processor: {
    enableDebugLogs: false,
    maxBufferSize: 2 * 1024 * 1024, // 2MB
    jsonParseRetries: 5,
    deduplicationCacheSize: 2000,
    enableDeduplication: true
  },
  reconnect: {
    maxRetries: 10,
    initialDelay: 500,
    maxDelay: 60000,
    backoffFactor: 1.5,
    connectionTimeout: 15000,
    enableAutoReconnect: true
  },
  monitoring: {
    enablePerformanceMonitoring: false,
    enableErrorTracking: true,
    reportingInterval: 300000, // 5分钟
    enableEventLogging: false
  }
};

/**
 * 开发环境调试配置
 */
export const DEVELOPMENT_SSE_CONFIG: SSEConfig = {
  processor: {
    enableDebugLogs: true,
    maxBufferSize: 512 * 1024, // 512KB
    jsonParseRetries: 2,
    deduplicationCacheSize: 500,
    enableDeduplication: true
  },
  reconnect: {
    maxRetries: 3,
    initialDelay: 2000,
    maxDelay: 10000,
    backoffFactor: 2,
    connectionTimeout: 5000,
    enableAutoReconnect: true
  },
  monitoring: {
    enablePerformanceMonitoring: true,
    enableErrorTracking: true,
    reportingInterval: 30000, // 30秒
    enableEventLogging: true
  }
};

/**
 * 获取当前环境的 SSE 配置
 */
export function getSSEConfig(): SSEConfig {
  const env = process.env.NODE_ENV || 'development';
  
  switch (env) {
    case 'production':
      return PRODUCTION_SSE_CONFIG;
    case 'development':
      return DEVELOPMENT_SSE_CONFIG;
    default:
      return DEFAULT_SSE_CONFIG;
  }
}

/**
 * 合并自定义配置
 */
export function mergeSSEConfig(customConfig: Partial<SSEConfig>): SSEConfig {
  const baseConfig = getSSEConfig();
  
  return {
    processor: { ...baseConfig.processor, ...customConfig.processor },
    reconnect: { ...baseConfig.reconnect, ...customConfig.reconnect },
    monitoring: { ...baseConfig.monitoring, ...customConfig.monitoring }
  };
}
