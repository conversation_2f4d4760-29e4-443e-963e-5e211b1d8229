/* 当贝AI Provider API 测试工具样式 */

/* CSS 变量定义 */
:root {
  /* 浅色主题 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #adb5bd;
  --border-color: #dee2e6;
  --border-radius: 8px;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  /* 主题色 */
  --primary-color: #007bff;
  --primary-hover: #0056b3;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  
  /* 状态码颜色 */
  --status-2xx: #28a745;
  --status-3xx: #ffc107;
  --status-4xx: #fd7e14;
  --status-5xx: #dc3545;
  
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 动画 */
  --transition: all 0.2s ease-in-out;
}

/* 深色主题 */
[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #404040;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-muted: #808080;
  --border-color: #404040;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: var(--transition);
}

/* 容器和布局 */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: var(--spacing-lg);
  min-height: calc(100vh - 80px);
}

/* 顶部导航栏 */
.header {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo-icon {
  font-size: 24px;
}

.logo h1 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  background: none;
  white-space: nowrap;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--border-color);
}

.btn-icon {
  padding: var(--spacing-sm);
  min-width: 36px;
  min-height: 36px;
}

.btn-small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 12px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 表单控件 */
.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  transition: var(--transition);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-group label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-row {
  display: flex;
  gap: var(--spacing-md);
  align-items: end;
}

.flex-1 {
  flex: 1;
}

/* 左侧边栏 */
.sidebar {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  height: fit-content;
  position: sticky;
  top: 100px;
  box-shadow: var(--shadow);
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.sidebar-header h2 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 搜索框 */
.search-box {
  margin-bottom: var(--spacing-lg);
}

.search-input {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  width: 100%;
  font-size: 14px;
}

/* API 分组 */
.api-group {
  margin-bottom: var(--spacing-lg);
}

.api-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-sm);
  cursor: pointer;
}

.api-group-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.api-group-toggle {
  font-size: 12px;
  color: var(--text-secondary);
  transition: var(--transition);
}

.api-group.collapsed .api-group-toggle {
  transform: rotate(-90deg);
}

.api-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.api-group.collapsed .api-list {
  display: none;
}

.api-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.api-item:hover {
  background-color: var(--bg-tertiary);
}

.api-item.active {
  background-color: var(--primary-color);
  color: white;
}

.api-method {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  text-transform: uppercase;
  min-width: 40px;
  text-align: center;
}

.api-method.get { background-color: var(--success-color); color: white; }
.api-method.post { background-color: var(--primary-color); color: white; }
.api-method.put { background-color: var(--warning-color); color: white; }
.api-method.delete { background-color: var(--danger-color); color: white; }
.api-method.patch { background-color: var(--info-color); color: white; }

.api-path {
  font-size: 12px;
  font-family: var(--font-mono);
  color: var(--text-secondary);
  flex: 1;
}

.api-item.active .api-path {
  color: rgba(255, 255, 255, 0.9);
}

/* 历史记录 */
.history-section {
  margin-top: var(--spacing-xl);
}

.history-section h3 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 12px;
}

.history-item:hover {
  background-color: var(--bg-tertiary);
}

.history-method {
  font-size: 9px;
  font-weight: 600;
  padding: 1px 4px;
  border-radius: 3px;
  text-transform: uppercase;
  min-width: 30px;
  text-align: center;
}

.history-url {
  font-family: var(--font-mono);
  color: var(--text-secondary);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-time {
  font-size: 10px;
  color: var(--text-muted);
}

/* 主要内容区域 */
.content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* 区域标题 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.section-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.section-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 请求构建器 */
.request-builder {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow);
}

.request-basic {
  margin-bottom: var(--spacing-lg);
}

/* 选项卡 */
.tabs {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.tab-headers {
  display: flex;
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.tab-header {
  flex: 1;
  padding: var(--spacing-md);
  border: none;
  background: none;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  border-right: 1px solid var(--border-color);
}

.tab-header:last-child {
  border-right: none;
}

.tab-header:hover {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.tab-header.active {
  background-color: var(--bg-primary);
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
}

.tab-content {
  display: none;
  padding: var(--spacing-lg);
  background-color: var(--bg-primary);
}

.tab-content.active {
  display: block;
}

/* 编辑器工具栏 */
.editor-toolbar {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

/* 代码编辑器 */
.code-editor {
  font-family: var(--font-mono);
  font-size: 13px;
  line-height: 1.5;
  resize: vertical;
  min-height: 120px;
}

.code-viewer {
  font-family: var(--font-mono);
  font-size: 13px;
  line-height: 1.5;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 400px;
  overflow-y: auto;
}

/* 响应显示器 */
.response-viewer {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow);
}

/* 响应状态 */
.response-status {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.status-info {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

.status-code {
  font-size: 18px;
  font-weight: 600;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  color: white;
}

.status-code.status-2xx { background-color: var(--status-2xx); }
.status-code.status-3xx { background-color: var(--status-3xx); }
.status-code.status-4xx { background-color: var(--status-4xx); }
.status-code.status-5xx { background-color: var(--status-5xx); }

.status-text {
  font-weight: 500;
  color: var(--text-primary);
}

.response-time,
.response-size {
  font-size: 12px;
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
}

/* 查看器工具栏 */
.viewer-toolbar {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.viewer-toolbar .form-control {
  width: auto;
  min-width: 120px;
}

/* 流式响应 */
.stream-toolbar {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.stream-status {
  font-size: 12px;
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  margin-left: auto;
}

.stream-status.connected {
  color: var(--success-color);
  background-color: rgba(40, 167, 69, 0.1);
}

.stream-status.error {
  color: var(--danger-color);
  background-color: rgba(220, 53, 69, 0.1);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .layout {
    grid-template-columns: 250px 1fr;
  }
}

@media (max-width: 768px) {
  .layout {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .sidebar {
    position: static;
    order: 2;
  }
  
  .content {
    order: 1;
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .tab-headers {
    flex-wrap: wrap;
  }
  
  .tab-header {
    flex: none;
    min-width: 80px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  .layout {
    gap: var(--spacing-sm);
  }

  .request-builder,
  .response-viewer,
  .sidebar {
    padding: var(--spacing-md);
  }

  .status-info {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

/* 加载指示器 */
.loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(2px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: var(--spacing-md);
  color: var(--text-primary);
  font-weight: 500;
  background-color: var(--bg-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 通知样式 */
.notifications {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 1500;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-width: 400px;
}

.notification {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  animation: slideIn 0.3s ease-out;
}

.notification.success {
  border-left: 4px solid var(--success-color);
}

.notification.error {
  border-left: 4px solid var(--danger-color);
}

.notification.warning {
  border-left: 4px solid var(--warning-color);
}

.notification.info {
  border-left: 4px solid var(--info-color);
}

.notification-icon {
  font-size: 16px;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.notification-message {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  margin-top: 2px;
}

.notification-close:hover {
  color: var(--text-primary);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 键值对编辑器 */
.key-value-editor {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.key-value-row {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.key-value-row input {
  flex: 1;
}

.key-value-row .key-input {
  max-width: 200px;
}

.key-value-row .remove-btn {
  padding: var(--spacing-xs);
  background-color: var(--danger-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 12px;
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.key-value-row .remove-btn:hover {
  background-color: #c82333;
}

/* 主题切换动画 */
* {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* 代码高亮 */
.json-key {
  color: #0451a5;
}

.json-string {
  color: #a31515;
}

.json-number {
  color: #098658;
}

.json-boolean {
  color: #0000ff;
}

.json-null {
  color: #808080;
}

[data-theme="dark"] .json-key {
  color: #9cdcfe;
}

[data-theme="dark"] .json-string {
  color: #ce9178;
}

[data-theme="dark"] .json-number {
  color: #b5cea8;
}

[data-theme="dark"] .json-boolean {
  color: #569cd6;
}

[data-theme="dark"] .json-null {
  color: #808080;
}

/* 工具提示 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: var(--text-primary);
  color: var(--bg-primary);
  text-align: center;
  border-radius: var(--border-radius);
  padding: var(--spacing-xs) var(--spacing-sm);
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--text-primary) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-muted);
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
}

.empty-state-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
}

.empty-state-description {
  font-size: 14px;
  line-height: 1.5;
}
