/**
 * 改进的 SSE 数据处理器
 * 解决数据分块、JSON 解析和事件处理的问题
 */

import { ChatCallbacks, SSEMessageDelta, SSEChatCompleted, DangbeiApiError, ErrorType } from '../types';

/**
 * SSE 事件状态
 */
interface SSEEventState {
  event: string;
  data: string[];
  id?: string;
  retry?: number;
}

/**
 * 改进的 SSE 处理器
 * 支持跨包数据重组、智能缓冲和错误恢复
 */
export class ImprovedSSEProcessor {
  private buffer: string = '';
  private currentEvent: SSEEventState | null = null;
  private messageSequence: number = 0;
  private processedMessages: Set<string> = new Set();

  /**
   * 处理流式响应数据
   * 
   * @param stream 流式响应对象
   * @param callbacks 回调函数
   */
  public async processStream(
    stream: NodeJS.ReadableStream,
    callbacks: ChatCallbacks
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      let chatCompleted = false;

      stream.on('data', (chunk: Buffer) => {
        try {
          this.processChunk(chunk, callbacks, (completed) => {
            if (completed) {
              chatCompleted = true;
            }
          });
        } catch (error) {
          console.error('处理数据块时出错:', error);
          // 不立即失败，继续处理后续数据
        }
      });

      stream.on('end', () => {
        // 处理缓冲区中剩余的数据
        this.flushBuffer(callbacks);
        
        // 如果没有收到完成事件，发送默认完成事件
        if (!chatCompleted && callbacks.onComplete) {
          callbacks.onComplete({
            id: '',
            parentMsgId: '',
            conversation_id: '',
            supportDownload: false
          });
        }
        resolve();
      });

      stream.on('error', (error) => {
        const apiError = new DangbeiApiError(
          `流式响应处理失败: ${error.message}`,
          ErrorType.NETWORK_ERROR
        );

        if (callbacks.onError) {
          callbacks.onError(apiError);
        }
        reject(apiError);
      });
    });
  }

  /**
   * 处理数据块
   * 
   * @param chunk 数据块
   * @param callbacks 回调函数
   * @param onComplete 完成回调
   */
  private processChunk(
    chunk: Buffer,
    callbacks: ChatCallbacks,
    onComplete: (completed: boolean) => void
  ): void {
    const chunkStr = chunk.toString('utf8');
    this.buffer += chunkStr;

    // 按行分割，保留不完整的行
    const lines = this.buffer.split('\n');
    this.buffer = lines.pop() || '';

    for (const line of lines) {
      this.processLine(line, callbacks, onComplete);
    }
  }

  /**
   * 处理单行数据
   * 
   * @param line 行数据
   * @param callbacks 回调函数
   * @param onComplete 完成回调
   */
  private processLine(
    line: string,
    callbacks: ChatCallbacks,
    onComplete: (completed: boolean) => void
  ): void {
    const trimmedLine = line.trim();

    // 空行表示事件结束
    if (trimmedLine === '') {
      if (this.currentEvent) {
        const completed = this.processEvent(this.currentEvent, callbacks);
        if (completed) {
          onComplete(true);
        }
        this.currentEvent = null;
      }
      return;
    }

    // 解析 SSE 字段
    if (trimmedLine.startsWith('event:')) {
      this.initializeEvent();
      this.currentEvent!.event = trimmedLine.substring(6).trim();
    } else if (trimmedLine.startsWith('data:')) {
      this.initializeEvent();
      const dataContent = trimmedLine.substring(5).trim();
      this.currentEvent!.data.push(dataContent);
    } else if (trimmedLine.startsWith('id:')) {
      this.initializeEvent();
      this.currentEvent!.id = trimmedLine.substring(3).trim();
    } else if (trimmedLine.startsWith('retry:')) {
      this.initializeEvent();
      this.currentEvent!.retry = parseInt(trimmedLine.substring(6).trim(), 10);
    }
  }

  /**
   * 初始化当前事件
   */
  private initializeEvent(): void {
    if (!this.currentEvent) {
      this.currentEvent = {
        event: '',
        data: []
      };
    }
  }

  /**
   * 处理完整的 SSE 事件
   * 
   * @param eventState 事件状态
   * @param callbacks 回调函数
   * @returns 是否为完成事件
   */
  private processEvent(eventState: SSEEventState, callbacks: ChatCallbacks): boolean {
    try {
      // 合并多行数据
      const dataContent = eventState.data.join('\n');
      
      if (!dataContent) {
        return false;
      }

      // 尝试解析 JSON，支持多次尝试
      const parsedData = this.parseJSONWithRetry(dataContent);
      if (!parsedData) {
        console.warn('无法解析 SSE 数据:', dataContent);
        return false;
      }

      // 检查消息去重
      const messageId = parsedData.id || `${this.messageSequence++}`;
      if (this.processedMessages.has(messageId)) {
        console.log('跳过重复消息:', messageId);
        return false;
      }
      this.processedMessages.add(messageId);

      // 根据事件类型处理
      return this.handleParsedEvent(eventState.event, parsedData, callbacks);

    } catch (error) {
      console.error('处理 SSE 事件时出错:', error);
      return false;
    }
  }

  /**
   * 带重试的 JSON 解析
   * 
   * @param data 数据内容
   * @returns 解析结果
   */
  private parseJSONWithRetry(data: string): any | null {
    // 尝试直接解析
    try {
      return JSON.parse(data);
    } catch (error) {
      // 尝试修复常见的 JSON 问题
      const fixedData = this.fixCommonJSONIssues(data);
      try {
        return JSON.parse(fixedData);
      } catch (retryError) {
        console.warn('JSON 解析失败:', retryError, '原始数据:', data);
        return null;
      }
    }
  }

  /**
   * 修复常见的 JSON 问题
   * 
   * @param data 原始数据
   * @returns 修复后的数据
   */
  private fixCommonJSONIssues(data: string): string {
    // 移除可能的 BOM 标记
    let fixed = data.replace(/^\uFEFF/, '');
    
    // 移除前后空白字符
    fixed = fixed.trim();
    
    // 尝试修复不完整的 JSON（如缺少结束括号）
    if (fixed.startsWith('{') && !fixed.endsWith('}')) {
      // 简单的括号匹配修复
      const openBraces = (fixed.match(/\{/g) || []).length;
      const closeBraces = (fixed.match(/\}/g) || []).length;
      const missingBraces = openBraces - closeBraces;
      
      if (missingBraces > 0) {
        fixed += '}'.repeat(missingBraces);
      }
    }
    
    return fixed;
  }

  /**
   * 处理解析后的事件
   * 
   * @param eventType 事件类型
   * @param data 解析后的数据
   * @param callbacks 回调函数
   * @returns 是否为完成事件
   */
  private handleParsedEvent(
    eventType: string,
    data: any,
    callbacks: ChatCallbacks
  ): boolean {
    switch (eventType) {
      case 'conversation.message.delta':
        if (callbacks.onMessage && data.content) {
          const deltaData: SSEMessageDelta = {
            role: data.role || 'assistant',
            type: data.type || 'answer',
            content: data.content,
            content_type: data.content_type || 'text',
            id: data.id || '',
            parentMsgId: data.parentMsgId || '',
            conversation_id: data.conversation_id || '',
            created_at: data.created_at || Date.now(),
            requestId: data.requestId || '',
            supportDownload: data.supportDownload || false
          };
          callbacks.onMessage(data.content, deltaData);
        }
        return false;

      case 'conversation.message.completed':
      case 'conversation.chat.completed':
        if (callbacks.onComplete) {
          const completedData: SSEChatCompleted = {
            id: data.id || '',
            parentMsgId: data.parentMsgId || '',
            conversation_id: data.conversation_id || '',
            supportDownload: data.supportDownload || false
          };
          callbacks.onComplete(completedData);
        }
        return true;

      default:
        // 记录未知事件类型，但不阻止处理
        console.log('收到未知事件类型:', eventType, data);
        return false;
    }
  }

  /**
   * 刷新缓冲区中剩余的数据
   * 
   * @param callbacks 回调函数
   */
  private flushBuffer(callbacks: ChatCallbacks): void {
    if (this.buffer.trim()) {
      console.log('处理缓冲区剩余数据:', this.buffer);
      // 尝试处理剩余数据
      this.processLine(this.buffer, callbacks, () => {});
      this.buffer = '';
    }

    // 处理未完成的事件
    if (this.currentEvent) {
      this.processEvent(this.currentEvent, callbacks);
      this.currentEvent = null;
    }
  }

  /**
   * 重置处理器状态
   */
  public reset(): void {
    this.buffer = '';
    this.currentEvent = null;
    this.messageSequence = 0;
    this.processedMessages.clear();
  }
}
