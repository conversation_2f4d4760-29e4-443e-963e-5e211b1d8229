/**
 * 聊天服务
 * 负责处理聊天消息的发送和接收
 */

import {
  ChatRequest,
  ChatOptions,
  ChatResponse,
  ChatCallbacks,
  SSEMessageDelta,
  SSEChatCompleted,
  DangbeiApiError,
  ErrorType
} from '../types';
import { HttpClient } from './http-client';
import { SSEClient } from './sse-client';
import { CommonService } from './common-service';
import { V2ErrorHandler } from '../utils/v2-error-handler';
import { ImprovedSSEProcessor } from './improved-sse-processor';

/**
 * 聊天服务类
 * 提供聊天消息发送和流式响应处理
 */
export class ChatService {
  private readonly httpClient: HttpClient;
  private readonly sseClient: SSEClient;
  private readonly commonService: CommonService;
  private readonly sseProcessor: ImprovedSSEProcessor;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
    this.sseClient = new SSEClient();
    this.commonService = new CommonService(httpClient);
    this.sseProcessor = new ImprovedSSEProcessor();
  }

  /**
   * 发送聊天消息（流式响应）
   *
   * @param options 聊天选项
   * @param callbacks 回调函数（可选）
   * @returns 聊天响应或void
   */
  public async chat(options: ChatOptions): Promise<ChatResponse>;
  public async chat(options: ChatOptions, callbacks: ChatCallbacks): Promise<void>;
  public async chat(options: ChatOptions, callbacks?: ChatCallbacks): Promise<ChatResponse | void> {
    // 生成消息ID
    const messageId = await this.commonService.generateId();

    // 构建聊天请求
    const chatRequest = await this.buildChatRequest(options, messageId);

    // 如果提供了回调函数，直接使用流式模式
    if (callbacks) {
      return this.sendChatRequest(chatRequest, callbacks);
    }

    // 否则使用同步模式，收集所有消息后返回
    let fullContent = '';
    let lastMessageData: SSEMessageDelta | null = null;

    return new Promise((resolve, reject) => {
      // 设置内部回调函数
      const internalCallbacks: ChatCallbacks = {
        onMessage: (content: string, data: SSEMessageDelta) => {
          fullContent += content;
          lastMessageData = data;

          // 调用用户提供的回调
          if (options.callbacks?.onMessage) {
            options.callbacks.onMessage(content, data);
          }
        },

        onComplete: (data: SSEChatCompleted) => {
          // 调用用户提供的回调
          if (options.callbacks?.onComplete) {
            options.callbacks.onComplete(data);
          }

          // 构建最终响应
          const response: ChatResponse = {
            content: fullContent,
            messageId: data.id,
            parentMessageId: data.parentMsgId,
            conversationId: data.conversation_id,
            requestId: lastMessageData?.requestId || ''
          };

          resolve(response);
        },

        onError: (error: Error) => {
          // 调用用户提供的错误回调
          if (options.callbacks?.onError) {
            options.callbacks.onError(error);
          }

          reject(error);
        }
      };

      // 发送聊天请求并处理SSE响应
      this.sendChatRequest(chatRequest, internalCallbacks).catch(reject);
    });
  }

  /**
   * 发送聊天消息（同步响应）
   * 等待完整响应后返回
   * 
   * @param options 聊天选项
   * @returns 完整的响应内容
   */
  public async chatSync(options: ChatOptions): Promise<string> {
    const response = await this.chat(options);
    return response.content;
  }

  /**
   * 构建聊天请求参数
   * 
   * @param options 聊天选项
   * @param messageId 消息ID
   * @returns 聊天请求对象
   */
  private async buildChatRequest(options: ChatOptions, messageId: string): Promise<ChatRequest> {
    return {
      stream: true,
      botCode: options.botCode || 'AI_SEARCH',
      conversationId: options.conversationId,
      question: options.question,
      model: options.model || 'doubao-1_6-thinking',
      chatOption: {
        searchKnowledge: options.chatOption?.searchKnowledge || false,
        searchAllKnowledge: options.chatOption?.searchAllKnowledge || false,
        searchSharedKnowledge: options.chatOption?.searchSharedKnowledge || false
      },
      knowledgeList: [],
      anonymousKey: '',
      uuid: messageId,
      chatId: messageId,
      files: [],
      reference: [],
      role: 'user',
      status: 'local',
      content: options.question,
      userAction: '',
      agentId: ''
    };
  }

  /**
   * 发送聊天请求并处理SSE响应
   *
   * @param chatRequest 聊天请求
   * @param callbacks 回调函数
   */
  private async sendChatRequest(
    chatRequest: ChatRequest,
    callbacks: ChatCallbacks
  ): Promise<void> {
    try {
      console.log('[ChatService] 发送聊天请求，使用改进的 SSE 处理器');

      // 重置处理器状态
      this.sseProcessor.reset();

      // 发送POST请求并获取流式响应
      const stream = await this.httpClient.postStream('/ai-search/chatApi/v2/chat', chatRequest);

      // 使用改进的 SSE 处理器处理流式数据
      await this.sseProcessor.processStream(stream, callbacks);

    } catch (error) {
      // 特殊处理v2接口错误
      const chatUrl = `${this.httpClient['baseURL']}/ai-search/chatApi/v2/chat`;
      const v2ErrorInfo = V2ErrorHandler.analyzeV2Error(error, chatUrl, chatRequest);

      // 在调试模式下输出详细的v2错误分析
      if (process.env['NODE_ENV'] === 'development') {
        const errorReport = V2ErrorHandler.generateErrorReport(v2ErrorInfo);
        console.log(errorReport);
      }

      const apiError = error instanceof DangbeiApiError ? error :
        new DangbeiApiError(
          `聊天请求失败: ${error instanceof Error ? error.message : '未知错误'}`,
          ErrorType.NETWORK_ERROR
        );

      if (callbacks.onError) {
        callbacks.onError(apiError);
      } else {
        throw apiError;
      }
    }
  }

  /**
   * 处理流式响应数据
   * 解析SSE格式的数据并调用相应的回调函数
   *
   * @param stream 流式响应对象
   * @param callbacks 回调函数
   */
  private async processStreamResponse(
    stream: NodeJS.ReadableStream,
    callbacks: ChatCallbacks
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      let buffer = '';
      let chatCompleted = false;

      stream.on('data', (chunk: Buffer) => {
        const chunkStr = chunk.toString();
        buffer += chunkStr;

        // 处理完整的SSE消息
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留不完整的行

        let currentEvent = '';
        let currentData = '';

        for (const line of lines) {
          if (line.startsWith('event:')) {
            // 如果有之前的消息，先处理它
            if (currentEvent && currentData) {
              const completed = this.handleSSEMessage(currentEvent, currentData, callbacks);
              if (completed) {
                chatCompleted = true;
              }
            }
            currentEvent = line.substring(6).trim();
            currentData = '';
          } else if (line.startsWith('data:')) {
            currentData = line.substring(5).trim();
            // 检查JSON是否完整
            if (currentEvent && currentData && this.isValidJSON(currentData)) {
              const completed = this.handleSSEMessage(currentEvent, currentData, callbacks);
              if (completed) {
                chatCompleted = true;
              }
              currentEvent = '';
              currentData = '';
            }
          }
        }
      });

      stream.on('end', () => {
        // 只有在没有收到完成事件时才调用onComplete
        if (!chatCompleted && callbacks.onComplete) {
          callbacks.onComplete({
            id: '',
            parentMsgId: '',
            conversation_id: '',
            supportDownload: false
          });
        }
        resolve();
      });

      stream.on('error', (error) => {
        const apiError = new DangbeiApiError(
          `流式响应处理失败: ${error.message}`,
          ErrorType.NETWORK_ERROR
        );

        if (callbacks.onError) {
          callbacks.onError(apiError);
        }
        reject(apiError);
      });
    });
  }

  /**
   * 处理单个SSE消息
   *
   * @param event 事件类型
   * @param data 数据内容
   * @param callbacks 回调函数
   * @returns 是否为完成事件
   */
  private handleSSEMessage(
    event: string,
    data: string,
    callbacks: ChatCallbacks
  ): boolean {
    try {
      // 解析JSON数据
      const parsedData = JSON.parse(data);

      // 根据事件类型处理不同的消息
      switch (event) {
        case 'conversation.message.delta':
          if (callbacks.onMessage && parsedData.content) {
            const deltaData: SSEMessageDelta = {
              role: parsedData.role || 'assistant',
              type: parsedData.type || 'answer',
              content: parsedData.content,
              content_type: parsedData.content_type || 'text',
              id: parsedData.id || '',
              parentMsgId: parsedData.parentMsgId || '',
              conversation_id: parsedData.conversation_id || '',
              created_at: parsedData.created_at || Date.now(),
              requestId: parsedData.requestId || '',
              supportDownload: parsedData.supportDownload || false
            };
            callbacks.onMessage(parsedData.content, deltaData);
          }
          return false;

        case 'conversation.message.completed':
        case 'conversation.chat.completed':
          if (callbacks.onComplete) {
            callbacks.onComplete({
              id: parsedData.id || '',
              parentMsgId: parsedData.parentMsgId || '',
              conversation_id: parsedData.conversation_id || '',
              supportDownload: parsedData.supportDownload || false
            });
          }
          return true;

        default:
          // 忽略其他类型的事件（如follow_up等）
          return false;
      }
    } catch (error) {
      // 静默处理JSON解析错误，因为数据可能被截断
      return false;
    }
  }

  /**
   * 检查字符串是否为有效的JSON
   *
   * @param str 要检查的字符串
   * @returns 是否为有效JSON
   */
  private isValidJSON(str: string): boolean {
    try {
      JSON.parse(str);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 停止当前聊天
   */
  public stopChat(): void {
    this.sseClient.close();
  }

  /**
   * 检查聊天服务状态
   * 
   * @returns 服务状态信息
   */
  public getStatus(): {
    isConnected: boolean;
    readyState: string;
  } {
    return {
      isConnected: this.sseClient.isConnectedToStream(),
      readyState: this.sseClient.getReadyState()
    };
  }

  /**
   * 验证聊天选项
   * 
   * @param options 聊天选项
   * @returns 验证结果
   */
  public validateChatOptions(options: ChatOptions): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!options.conversationId || !options.conversationId.trim()) {
      errors.push('对话ID不能为空');
    }

    if (!options.question || !options.question.trim()) {
      errors.push('问题内容不能为空');
    }

    if (options.question && options.question.length > 10000) {
      errors.push('问题内容过长，最大支持10000字符');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
